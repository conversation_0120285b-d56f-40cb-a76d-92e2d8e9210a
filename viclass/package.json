{"name": "viclass", "version": "0.0.0", "scripts": {"docker-workspace": "docker build -f workspace.DOCKERFILE . -t viclass/workspace", "ng": "node --max_old_space_size=10000 ./node_modules/@angular/cli/bin/ng", "serve-test.embed": "ng serve @viclass/test.embed", "clean": "rmdir /s dist", "config.server": "yarn workspace @viclass/config.server", "watch-config.server": "yarn workspace @viclass/config.server start", "build-config.server": "yarn workspace @viclass/config.server build", "eb.word": "yarn workspace @viclass/eb.word", "build-eb.word": "yarn workspace @viclass/eb.word build", "build-themes": "yarn --cwd ./packages/themes/viclass/themes build", "build-proto": "yarn --cwd ./packages/protobuf/viclass/proto build", "build-editor.word.transform": "yarn --cwd ./packages/editors/viclass/editor.word.transform build --configuration=development", "build-editor.core": "ng build @viclass/editor.core --configuration=development", "build-editor.coord": "ng build @viclass/editor.coordinator --configuration=development", "build-editor.fred": "ng build @viclass/editor.freedrawing --configuration=development", "build-editor.geo": "ng build @viclass/editor.geo --configuration=development", "build-editor.word": "ng build @viclass/editor.word --configuration=development", "build-editor.math": "ng build @viclass/editor.math --configuration=development", "build-editor.magh": "ng build @viclass/editor.magh --configuration=development", "build-editor": "yarn build-proto && yarn build-editor.core && yarn build-editor.coord && yarn build-editor.word.transform && yarn conc \"yarn:build-editor.fred\" \"yarn:build-editor.geo\" \"yarn:build-editor.word\" \"yarn:build-editor.math\" \"yarn:build-editor.magh\"", "build-editorui.commontools": "yarn build-editorui.commontools-style && ng build @viclass/editorui.commontools --configuration=development", "build-editorui.classroomtools": "yarn build-editorui.classroomtools-style && ng build @viclass/editorui.classroomtools --configuration=development", "build-editorui.loader": "ng build @viclass/editorui.loader --configuration=development", "build-editorui.fred": "yarn build-editorui.fred-style && ng build @viclass/editorui.freedrawing --configuration=development", "build-editorui.geo": "yarn build-editorui.geo-style && ng build @viclass/editorui.geo --configuration=development", "build-editorui.word": "yarn build-editorui.word-style && ng build @viclass/editorui.word --configuration=development", "build-editorui.math": "yarn build-editorui.math-style && ng build @viclass/editorui.math --configuration=development", "build-editorui.magh": "yarn build-editorui.magh-style && ng build @viclass/editorui.magh --configuration=development", "build-editorui": "yarn build-editorui.loader && yarn build-editorui.commontools && yarn build-editorui.classroomtools && yarn conc \"yarn build-editorui.fred\" \"yarn build-editorui.geo\" \"yarn build-editorui.word\" \"yarn build-editorui.math\" \"yarn build-editorui.magh\"", "build-portal.common-style": "cd ./packages/portal/viclass/portal.common && npx tailwindcss -c ./tailwind.config.js  -o ./assets/output.css -m", "build-portal.common": "yarn build-portal.common-style && ng build @viclass/portal.common --configuration=development", "build-portal.homepage": "ng build @viclass/portal.homepage --configuration=development", "build-portal.homepage-server": "ng build @viclass/portal.homepage --configuration=development && ng run @viclass/portal.homepage:server", "build-portal.classroom": "ng build @viclass/portal.classrooms --configuration=development", "build-portal.support": "yarn workspace portal-support build", "build-portal": "yarn build-portal.common && yarn && yarn conc \"yarn build-portal.homepage\" \"yarn build-portal.classroom\"", "build-mfe-container": "ng build @viclass/mfe --configuration=development --aot=false --build-optimizer=false", "build-mfe-style": "cd ./packages/mfe/viclass/mfe && npx webpack --config ./webpack.style.js", "build-editorui.commontools-style": "cd ./packages/editoruis/viclass/commontools && npx tailwindcss -c ./tailwind.config.js  -o ./assets/output.css -m", "build-editorui.classroomtools-style": "cd ./packages/editoruis/viclass/classroomtools && npx tailwindcss -c ./tailwind.config.js  -o ./assets/output.css -m", "build-editorui.fred-style": "cd ./packages/editoruis/viclass/editorui.freedrawing && npx tailwindcss -c ./tailwind.config.js  -o ./assets/output.css -m", "build-editorui.geo-style": "cd ./packages/editoruis/viclass/editorui.geo && npx tailwindcss -c ./tailwind.config.js  -o ./assets/output.css -m", "build-editorui.word-style": "cd ./packages/editoruis/viclass/editorui.word && npx tailwindcss -c ./tailwind.config.js  -o ./assets/output.css -m", "build-editorui.math-style": "cd ./packages/editoruis/viclass/editorui.math && npx tailwindcss -c ./tailwind.config.js  -o ./assets/output.css -m", "build-editorui.magh-style": "cd ./packages/editoruis/viclass/editorui.magh && npx tailwindcss -c ./tailwind.config.js  -o ./assets/output.css -m", "build-mfe": "yarn conc \"yarn build-mfe-container\" \"yarn build-mfe-style\"", "build-ww": "cd ./packages/mfe/viclass/ww && npx webpack --config ./webpack.dev.js", "build-all": "yarn build-themes && yarn build-editor && yarn build-editorui && yarn build-ww && yarn build-portal && yarn build-mfe", "build-full": "yarn && yarn build-proto && (yarn build-ww || true) && yarn && yarn build-editor.core && yarn && yarn build-editor.word.transform && yarn build-editor.coord && yarn && yarn build-themes && yarn build-editor && yarn build-editorui && yarn && yarn build-config.server && yarn && yarn build-ww && yarn && yarn build-mfe && yarn build-portal", "dist-editor.word.transform": "yarn --cwd ./packages/editors/viclass/editor.word.transform build --configuration=production", "dist-editor.core": "ng build @viclass/editor.core --configuration=production", "dist-editor.coord": "ng build @viclass/editor.coordinator --configuration=production", "dist-editor.fred": "ng build @viclass/editor.freedrawing --configuration=production", "dist-editor.geo": "ng build @viclass/editor.geo --configuration=production", "dist-editor.word": "ng build @viclass/editor.word --configuration=production", "dist-editor.math": "ng build @viclass/editor.math --configuration=production", "dist-editor.magh": "ng build @viclass/editor.magh --configuration=production", "dist-editor": "yarn build-proto && yarn dist-editor.core && yarn dist-editor.word.transform && yarn dist-editor.coord && yarn conc \"yarn:dist-editor.fred\" \"yarn:dist-editor.geo\" \"yarn:dist-editor.word\" \"yarn:dist-editor.math\" \"yarn:dist-editor.magh\"", "dist-editorui.loader": "ng build @viclass/editorui.loader --configuration=production", "dist-editorui.fred": "yarn build-editorui.fred-style && ng build @viclass/editorui.freedrawing --configuration=production", "dist-editorui.geo": "yarn build-editorui.geo-style && ng build @viclass/editorui.geo --configuration=production", "dist-editorui.word": "yarn build-editorui.word-style && ng build @viclass/editorui.word --configuration=production", "dist-editorui.math": "yarn build-editorui.math-style && ng build @viclass/editorui.math --configuration=production", "dist-editorui.magh": "yarn build-editorui.magh-style && ng build @viclass/editorui.magh --configuration=production", "dist-editorui.commontools": "yarn build-editorui.commontools-style && ng build @viclass/editorui.commontools --configuration=production", "dist-editorui.classroomtools": "yarn build-editorui.classroomtools-style && ng build @viclass/editorui.classroomtools --configuration=production", "dist-editorui": "yarn dist-editorui.loader && yarn dist-editorui.commontools && yarn dist-editorui.classroomtools && yarn conc \"yarn dist-editorui.fred\" \"yarn dist-editorui.geo\" \"yarn dist-editorui.word\" \"yarn dist-editorui.math\" \"yarn dist-editorui.magh\"", "dist-portal.common": "yarn build-portal.common-style && ng build @viclass/portal.common --configuration=production", "dist-portal.homepage": "ng build @viclass/portal.homepage --configuration=production", "dist-portal.homepage-server": "ng run @viclass/portal.homepage:prerender", "dist-portal.classroom": "ng build @viclass/portal.classrooms --configuration=production", "dist-portal.support": "yarn workspace portal-support build", "dist-portal": "yarn dist-portal.common && yarn conc \"yarn:dist-portal.homepage-server\" \"yarn:dist-portal.classroom\"", "dist-mfe-style": "cd ./packages/mfe/viclass/mfe && npx webpack --config ./webpack.style.prod.js", "dist-mfe-container": "ng build @viclass/mfe --configuration=production", "dist-mfe": "yarn conc \"yarn:dist-mfe-container\" \"yarn:dist-mfe-style\"", "dist-ww": "cd ./packages/mfe/viclass/ww && npx webpack --config ./webpack.prod.js", "dist-all": "yarn build-themes && yarn dist-editor && yarn dist-editorui && yarn dist-ww && yarn dist-portal && yarn dist-mfe", "staging-editor.core": "ng build @viclass/editor.core --configuration=staging", "staging-editor.coord": "ng build @viclass/editor.coordinator --configuration=staging", "staging-editor.fred": "ng build @viclass/editor.freedrawing --configuration=staging", "staging-editor.geo": "ng build @viclass/editor.geo --configuration=staging", "staging-editor.word": "ng build @viclass/editor.word --configuration=staging", "staging-editor.math": "ng build @viclass/editor.math --configuration=staging", "staging-editor.magh": "ng build @viclass/editor.magh --configuration=staging", "staging-editor": "yarn build-proto && yarn staging-editor.core && yarn dist-editor.word.transform && yarn staging-editor.coord && yarn conc \"yarn:staging-editor.fred\" \"yarn:staging-editor.geo\" \"yarn:staging-editor.word\" \"yarn:staging-editor.math\" \"yarn:staging-editor.magh\"", "staging-editorui.loader": "ng build @viclass/editorui.loader --configuration=staging", "staging-editorui.fred": "yarn build-editorui.fred-style && ng build @viclass/editorui.freedrawing --configuration=staging", "staging-editorui.geo": "yarn build-editorui.geo-style && ng build @viclass/editorui.geo --configuration=staging", "staging-editorui.word": "yarn build-editorui.word-style && ng build @viclass/editorui.word --configuration=staging", "staging-editorui.math": "yarn build-editorui.math-style && ng build @viclass/editorui.math --configuration=staging", "staging-editorui.magh": "yarn build-editorui.magh-style && ng build @viclass/editorui.magh --configuration=staging", "staging-editorui.commontools": "yarn build-editorui.commontools-style && ng build @viclass/editorui.commontools --configuration=staging", "staging-editorui.classroomtools": "yarn build-editorui.classroomtools-style && ng build @viclass/editorui.classroomtools --configuration=staging", "staging-editorui": "yarn staging-editorui.loader && yarn staging-editorui.commontools && yarn staging-editorui.classroomtools && yarn conc \"yarn staging-editorui.fred\" \"yarn staging-editorui.geo\" \"yarn staging-editorui.word\" \"yarn staging-editorui.math\" \"yarn staging-editorui.magh\"", "staging-portal.common": "yarn build-portal.common-style && ng build @viclass/portal.common --configuration=staging", "staging-portal.homepage": "ng build @viclass/portal.homepage --configuration=staging", "staging-portal.homepage-server": "ng run @viclass/portal.homepage:prerender:staging", "staging-portal.classroom": "ng build @viclass/portal.classrooms --configuration=staging", "staging-portal.support": "yarn workspace portal-support build", "staging-portal": "yarn staging-portal.common && yarn conc \"yarn:staging-portal.homepage-server\" \"yarn:staging-portal.classroom\"", "staging-mfe-style": "cd ./packages/mfe/viclass/mfe && npx webpack --config ./webpack.style.prod.js", "staging-mfe-container": "ng build @viclass/mfe --configuration=staging", "staging-mfe": "yarn conc \"yarn:staging-mfe-container\" \"yarn:staging-mfe-style\"", "staging-ww": "cd ./packages/mfe/viclass/ww && npx webpack --config ./webpack.prod.js", "staging-all": "yarn build-themes && yarn staging-editor && yarn staging-editorui && yarn staging-ww && yarn staging-portal && yarn staging-mfe", "watch-editor.core": "yarn build-editor.core --watch", "watch-editor.coord": "yarn build-editor.coord --watch", "watch-editor.fred": "yarn build-editor.fred --watch", "watch-editor.geo": "yarn build-editor.geo --watch", "watch-editor.word": "yarn build-editor.word --watch", "watch-editor.word.transform": "yarn --cwd ./packages/editors/viclass/editor.word.transform watch", "watch-editor.math": "yarn build-editor.math --watch", "watch-editor.magh": "yarn build-editor.magh --watch", "watch-editor": "yarn conc \"yarn:watch-editor.fred\" \"yarn:watch-editor.geo\" \"yarn:watch-editor.word\" \"yarn:watch-editor.math\" \"yarn:watch-editor.magh\"", "watch-editorui.commontools": "yarn build-editorui.commontools --watch", "watch-editorui.classroomtools": "yarn build-editorui.classroomtools --watch", "watch-editorui.loader": "yarn build-editorui.loader --watch", "watch-editorui.fred": "yarn build-editorui.fred --watch", "watch-editorui.geo": "yarn conc \"yarn build-editorui.geo --watch\" \"yarn build-editorui.geo-style -w\"", "watch-editorui.word": "yarn build-editorui.word --watch", "watch-editorui.math": "yarn build-editorui.math --watch", "watch-editorui.magh": "yarn build-editorui.magh --watch", "watch-editorui": "yarn conc \"yarn:watch-editorui.fred\" \"yarn:watch-editorui.geo\" \"yarn:watch-editorui.word\" \"yarn:watch-editorui.math\" \"yarn:watch-editorui.magh\"", "watch-portal.common": "yarn conc \"yarn build-portal.common --watch\" \"cd ./packages/portal/viclass/portal.common && npx tailwind -o ./assets/output.css -w -m\"", "watch-portal.homepage": "yarn build-portal.homepage --watch", "watch-portal.homepage-server": "cross-env NODE_TLS_REJECT_UNAUTHORIZED=0 ng run @viclass/portal.homepage:serve-ssr", "watch-portal.classroom": "yarn build-portal.classroom --watch", "watch-portal.support": "yarn workspace portal-support dev", "watch-portal": "yarn conc \"yarn:watch-portal.homepage\" \"yarn:watch-portal.classroom\"", "watch-mfe-container": "yarn build-mfe-container --watch", "watch-mfe-style": "yarn build-mfe-style --watch", "watch-mfe": "cross-env NODE_OPTIONS=--max-old-space-size=8192 yarn conc \"yarn:watch-mfe-container\" \"yarn:watch-mfe-style\"", "watch-ww": "yarn build-ww --watch", "watch-themes": "yarn build-themes --watch", "test-editor.geo": "yarn --cwd ./packages/editors/viclass/editor.geo test:jest", "serve-portal.homepage-server": "node dist/viclass/portal.homepage-server/main.js", "format": "npx prettier --config .prettierrc.json --ignore-path .prettierignore --write", "lint": "eslint -c eslint.config.mjs", "lint-fix": "yarn lint --fix . && yarn format .", "gen-dg": "node dependency_graph/dependency_graph.js && npx @mermaid-js/mermaid-cli -i dependency_graph/dependency_graph.mmd -o dependency_graph/dependency_graph.png -w 1200", "prepare": "cd .. && husky install viclass/.husky", "precommit": "lint-staged"}, "lint-staged": {"*.{js,mjs,ts,component.html}": ["yarn lint --fix"], "*.{json,css,scss}": ["yarn format"]}, "workspaces": ["dist/viclass/*", "devtool/*", "packages/backend/viclass/*", "packages/themes/viclass/themes", "packages/portal/viclass/portal.support", "packages/portal/starlight/packages/starlight", "../lib-jitsi-meet/dist", "../lib-mathlive/dist", "../lib-html-to-image/lib"], "private": true, "dependencies": {"@angular-architects/module-federation": "^16.0.0", "@angular-architects/module-federation-runtime": "^16.0.0", "@angular/animations": "^16.0.0", "@angular/cdk": "^16.0.0", "@angular/common": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/core": "^16.0.0", "@angular/elements": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/localize": "^16.0.0", "@angular/material": "^16.0.0", "@angular/material-moment-adapter": "^16.0.0", "@angular/platform-browser": "^16.0.0", "@angular/platform-browser-dynamic": "^16.0.0", "@angular/platform-server": "^16.0.0", "@angular/router": "^16.0.0", "@angular/ssr": "^18.2.10", "@babel/runtime": "^7.26.0", "@cortex-js/compute-engine": "^0.28.0", "@flatten-js/core": "^1.5.6", "@fullhuman/postcss-purgecss": "6.0.0", "@lexical/clipboard": "^0.31.0", "@lexical/code": "^0.31.0", "@lexical/file": "^0.31.0", "@lexical/hashtag": "^0.31.0", "@lexical/headless": "^0.31.0", "@lexical/history": "^0.31.0", "@lexical/link": "^0.31.0", "@lexical/list": "^0.31.0", "@lexical/mark": "^0.31.0", "@lexical/overflow": "^0.31.0", "@lexical/plain-text": "^0.31.0", "@lexical/rich-text": "^0.31.0", "@lexical/selection": "^0.31.0", "@lexical/table": "^0.31.0", "@lexical/text": "^0.31.0", "@lexical/utils": "^0.31.0", "@lexical/yjs": "^0.31.0", "@nguniversal/express-engine": "^16.2.0", "@popperjs/core": "^2.11.6", "@types/crypto-js": "^4.1.1", "@types/jquery": "^3.5.14", "@types/lodash": "^4.17.5", "angular-eslint": "^19.0.2", "axios": "1.7.2", "bezier-js": "^6.1.4", "bootstrap": "^5.2.2", "browser-image-compression": "^2.0.2", "clipper-lib": "^6.4.2", "cookie-parser": "^1.4.7", "copyfiles": "^2.4.1", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "express": "^4.15.2", "flowbite": "^2.2.1", "flowbite-datepicker": "^1.3.0", "google-protobuf": "^3.21.4", "idle-tracker": "^0.1.3", "interval-arithmetic": "^1.1.2", "jose": "5.4.1", "jquery": "^3.7.1", "lexical": "^0.31.0", "lib0": "^0.2.93", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-duration-format": "^2.3.2", "ng-hcaptcha": "^2.3.1", "ngx-build-plus": "^16.0.0", "ngx-cookie-service": "^18.0.0", "page-lifecycle": "^0.1.2", "pako": "^2.1.0", "perfect-freehand": "^1.2.2", "pixi.js": "^8.9.2", "polygon-clipping": "^0.15.7", "postcss": "8.4.38", "rxjs": "^7.8.1", "tailwindcss": "^3.4.4", "tslib": "^2.8.0", "y-protocols": "^1.0.6", "yjs": "^13.6.15", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.16", "@angular-eslint/builder": "^19.0.2", "@angular-eslint/eslint-plugin": "^19.0.2", "@angular-eslint/eslint-plugin-template": "^19.0.2", "@angular-eslint/schematics": "^19.0.2", "@angular-eslint/template-parser": "^19.0.2", "@angular/cli": "^16.0.0", "@angular/compiler-cli": "^16.0.0", "@angular/language-service": "^19.1.7", "@astrojs/node": "^8.3.4", "@nestjs/common": "^9.4.0", "@nestjs/core": "^9.4.0", "@nguniversal/builders": "^16.2.0", "@types/bezier-js": "^4.1.3", "@types/express": "^4.17.0", "@types/google.accounts": "^0.0.14", "@types/jasmine": "~4.0.0", "@types/moment-duration-format": "^2.2.3", "@types/node": "^22.13.9", "@types/pako": "^2.0.3", "@types/scroll-into-view": "^1.16.4", "@types/yargs": "^17.0.15", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "@vituum/vite-plugin-postcss": "^1.1.0", "autoprefixer": "^10.4.14", "bufferutil": "^4.0.9", "clean-webpack-plugin": "^4.0.0", "concurrently": "^9.0.1", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^8.0.0", "jasmine-core": "~4.3.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "lint-staged": "^15.2.7", "mini-css-extract-plugin": "^2.7.1", "ng-packagr": "^16.2.3", "nodemon": "^3.1.4", "pagefind": "^1.1.0", "postcss-import": "^15.1.0", "postcss-loader": "^7.3.0", "prettier": "^3.4.2", "prettier-eslint": "^16.3.0", "prettier-plugin-astro": "^0.14.1", "protoc-gen-grpc-web": "^1.5.0", "reflect-metadata": "^0.1.13", "sass": "^1.72.0", "sass-loader": "^13.3.2", "style-loader": "^3.3.1", "ts-loader": "^9.4.1", "typescript": "~5.1.6", "utf-8-validate": "^6.0.5", "vinyl-fs": "^3.0.3", "wait-on": "^6.0.1", "webpack": "^5.88.2", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-merge": "^5.9.0", "ws": "^8.18.1", "xmlhttprequest": "^1.8.0", "yargs": "^17.6.2"}}