import { <PERSON><PERSON>r<PERSON><PERSON><PERSON>Decora<PERSON>, UIPointerEventData } from '@viclass/editor.core';
import { syncPreviewCommands } from '../cmd';
import { geoDefaultHandlerFn, GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { RepeatSelector } from '../selectors';
import { nLines, SelectedStroke, strk } from '../selectors/common.selection';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { buildIntersectionRequest, getElementConstructionDetails } from './util.construction';
import {
    calculateCircleCircleIntersection,
    calculateCircleEllipseIntersection,
    calculateCircleSectorIntersection,
    calculateEllipseEllipseIntersection,
    calculateEllipseSectorIntersection,
    calculateLineCircleIntersection,
    calculateLineEllipseIntersection,
    calculateLineLineIntersection,
    calculateLineSectorIntersection,
    calculateSectorSectorIntersection,
} from './util.intersections';
import { assignNames, getFocusDocCtrl, handleIfPointerNotInError, isElementLine, remoteConstruct } from './util.tool';

/**
 * Intersection Point Tool - Creates intersection points between two geometric elements
 * Follows standardized geometry tool patterns for selection, preview, and construction
 *
 * IMPROVEMENTS MADE:
 * - Fixed ordering consistency between frontend (TypeScript) and backend (Kotlin)
 * - Enhanced Orders.pointsOnParallelVector to use normalized vectors like backend
 * - Improved Orders.pointsByRotation to match Kotlin's angleTo calculation method
 * - Ensured nth parameter calculation is consistent with backend expectations
 * - Maintained intersection order throughout preview and construction process
 */
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    override readonly toolType: GeometryToolType = 'IntersectionPointTool';

    declare selLogic: RepeatSelector<SelectedStroke>;
    private pQ = new PreviewQueue();
    private intersectionPreview: RenderVertex[] = [];
    private intersectionConstructed: RenderVertex[] = [];

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    /**
     * Creates selection logic using standardized selector pattern
     */
    private createSelLogic() {
        this.selLogic = nLines(this.pQ, this.pointerHandler.cursor, {
            count: 2,
            onComplete: this.performIntersectionPreview.bind(this),
        });
    }

    override resetState() {
        this.selLogic?.reset();
        this.intersectionPreview = [];
        this.intersectionConstructed = [];
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType === 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType === 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();
        return event;
    }

    /**
     * Handles selection attempts following standardized pattern
     */
    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        // Check if user clicked on existing intersection preview
        if (this.intersectionPreview.length > 0) {
            const hitCtx = ctrl.editor.checkHitInternal(ctrl.layers[0], event, false, true);
            const hitEl = hitCtx?.hitDetails?.el;

            if (hitEl && hitEl.type === 'RenderVertex') {
                const previewPoint = this.intersectionPreview.find(p => p.relIndex === hitEl.relIndex);
                if (previewPoint && event.eventType === 'pointerup') {
                    this.performConstruction(ctrl, previewPoint);
                    return;
                }
            }
        }

        // Try selection with the selector
        this.selLogic.trySelect(event, ctrl);
        this.pQ.flush(ctrl);
    }

    /**
     * Generates intersection preview when two elements are selected
     * Ensures consistent ordering with backend calculations
     */
    private async performIntersectionPreview(selector: RepeatSelector<SelectedStroke>, docCtrl: GeoDocCtrl) {
        const selectedElements = selector.selected || [];
        if (selectedElements.length !== 2) return;

        // Clear previous previews and constructed intersections
        this.intersectionPreview = [];
        this.intersectionConstructed = [];

        const [element1, element2] = selectedElements;

        // Extract StrokeType from SelectedStroke using strk helper
        const stroke1 = strk(element1);
        const stroke2 = strk(element2);

        // Calculate intersections based on element types
        const intersections = this.calculateIntersections(stroke1, stroke2, docCtrl);
        if (!intersections || intersections.length === 0) {
            this.resetState();
            return;
        }

        // Special case for line-line: construct immediately
        if (isElementLine(stroke1) && isElementLine(stroke2)) {
            if (intersections.length === 0) return;
            const intersectionVertex = pVertex(-9998, [intersections[0].x, intersections[0].y]);
            this.intersectionPreview.push(intersectionVertex);
            await this.performConstruction(docCtrl, intersectionVertex);
            return;
        }

        // Create preview points for other intersection types
        // The ordering is already correct from calculateIntersections
        this.createIntersectionPreviews(intersections, docCtrl);
    }

    /**
     * Calculates intersection points between two elements
     */
    private calculateIntersections(
        element1: StrokeType,
        element2: StrokeType,
        docCtrl: GeoDocCtrl
    ): { x: number; y: number }[] | null {
        try {
            // Line-Line intersection
            if (isElementLine(element1) && isElementLine(element2))
                return calculateLineLineIntersection(element1 as RenderLine, element2 as RenderLine, docCtrl);

            // Line-Circle intersection
            if (
                (isElementLine(element1) && element2.type === 'RenderCircle') ||
                (element1.type === 'RenderCircle' && isElementLine(element2))
            ) {
                const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
                const circle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;
                return calculateLineCircleIntersection(line, circle, docCtrl);
            }

            // Line-Ellipse intersection
            if (
                (isElementLine(element1) && element2.type === 'RenderEllipse') ||
                (element1.type === 'RenderEllipse' && isElementLine(element2))
            ) {
                const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
                const ellipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;
                return calculateLineEllipseIntersection(line, ellipse, docCtrl);
            }

            // Circle-Circle intersection
            if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle')
                return calculateCircleCircleIntersection(element1 as RenderCircle, element2 as RenderCircle, docCtrl);

            // Circle-Ellipse intersection
            if (
                (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
                (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
            ) {
                const circle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;
                const ellipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;
                return calculateCircleEllipseIntersection(circle, ellipse, docCtrl);
            }

            // Ellipse-Ellipse intersection
            if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse')
                return calculateEllipseEllipseIntersection(
                    element1 as RenderEllipse,
                    element2 as RenderEllipse,
                    docCtrl
                );

            // Line-Sector intersection
            if (
                (isElementLine(element1) && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && isElementLine(element2))
            ) {
                const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
                const sector = (element1.type === 'RenderSector' ? element1 : element2) as RenderSector;
                return calculateLineSectorIntersection(line, sector, docCtrl);
            }

            // Circle-Sector intersection
            if (
                (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
            ) {
                if (element1.type === 'RenderCircle') {
                    return calculateCircleSectorIntersection(
                        element1 as RenderCircle,
                        element2 as RenderSector,
                        docCtrl
                    );
                } else {
                    return calculateCircleSectorIntersection(
                        element2 as RenderCircle,
                        element1 as RenderSector,
                        docCtrl
                    );
                }
            }

            // Ellipse-Sector intersection
            if (
                (element1.type === 'RenderEllipse' && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && element2.type === 'RenderEllipse')
            ) {
                if (element1.type === 'RenderEllipse') {
                    return calculateEllipseSectorIntersection(
                        element1 as RenderEllipse,
                        element2 as RenderSector,
                        docCtrl
                    );
                } else {
                    return calculateEllipseSectorIntersection(
                        element2 as RenderEllipse,
                        element1 as RenderSector,
                        docCtrl
                    );
                }
            }

            // Sector-Sector intersection
            if (element1.type === 'RenderSector' && element2.type === 'RenderSector')
                return calculateSectorSectorIntersection(element1 as RenderSector, element2 as RenderSector, docCtrl);

            return null;
        } catch (error) {
            console.warn('Error calculating intersections:', error);
            return null;
        }
    }

    /**
     * Creates preview vertices for intersection points in the correct order
     * Order matches the backend calculation to ensure nth parameter consistency
     */
    private createIntersectionPreviews(intersections: { x: number; y: number }[], docCtrl: GeoDocCtrl) {
        // Create preview vertices in the same order as intersections array
        // This ensures nth parameter calculation matches backend behavior
        intersections.forEach((intersection, index) => {
            const previewVertex = pVertex(-9998 - index, [intersection.x, intersection.y]);
            this.intersectionPreview.push(previewVertex);
            syncPreviewCommands(previewVertex, docCtrl);
        });

        // Enable filtering for multiple intersection points
        if (this.intersectionPreview.length > 1) {
            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    /**
     * Performs construction of selected intersection point
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(docCtrl: GeoDocCtrl, intersectionSelected: RenderVertex) {
        const { pcs, points } = await assignNames(
            docCtrl,
            [intersectionSelected],
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Giao Điểm'
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        const construction = this.buildConstructionRequest(intersectionSelected, points[0].name);
        if (!construction) return;

        try {
            this.intersectionConstructed.push(intersectionSelected);
            await remoteConstruct(docCtrl, construction, [], this.editor.geoGateway, 'giao điểm');

            // Update remaining previews
            this.updateRemainingPreviews(docCtrl);
        } catch (e) {
            this.resetState();
            throw e;
        }
    }

    /**
     * Builds construction request based on selected elements and intersection point
     */
    private buildConstructionRequest(
        intersectionPoint: RenderVertex,
        pointName: string
    ): GeoElConstructionRequest | undefined {
        const selectedElements = this.selLogic.selected;
        if (!selectedElements || selectedElements.length !== 2) return undefined;

        const [element1, element2] = selectedElements;

        // Extract StrokeType from SelectedStroke using strk helper
        const stroke1 = strk(element1);
        const stroke2 = strk(element2);

        const elA = getElementConstructionDetails(stroke1 as any);
        const elB = getElementConstructionDetails(stroke2 as any);

        // Determine intersection type
        const cgName = this.getIntersectionTypeName(stroke1, stroke2);
        if (!cgName) return undefined;

        // Calculate nth parameter for multiple intersections
        const currentNth = this.calculateNthParameter(intersectionPoint.relIndex);

        return buildIntersectionRequest({
            cgName,
            outputName: pointName,
            paramA: elA,
            paramB: elB,
            nth: cgName === 'LineLine' ? undefined : currentNth,
        });
    }

    /**
     * Gets intersection type name for construction
     */
    private getIntersectionTypeName(element1: StrokeType, element2: StrokeType): string | undefined {
        if (isElementLine(element1) && isElementLine(element2)) return 'LineLine';
        if (
            (isElementLine(element1) && element2.type === 'RenderCircle') ||
            (element1.type === 'RenderCircle' && isElementLine(element2))
        )
            return 'LineCircle';
        if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle') return 'CircleCircle';
        if (
            (isElementLine(element1) && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && isElementLine(element2))
        )
            return 'LineEllipse';
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
        )
            return 'CircleEllipse';
        if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse') return 'EllipseEllipse';
        if (
            (isElementLine(element1) && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && isElementLine(element2))
        )
            return 'LineSector';
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
        )
            return 'CircleSector';
        if (
            (element1.type === 'RenderEllipse' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderEllipse')
        )
            return 'EllipseSector';
        if (element1.type === 'RenderSector' && element2.type === 'RenderSector') return 'SectorSector';
        return undefined;
    }

    /**
     * Calculates nth parameter for intersection point selection
     * Uses the original calculated intersections order to match backend ordering
     */
    private calculateNthParameter(relIdx: number): number | undefined {
        if (this.intersectionPreview.length === 1) return undefined;

        // Find the index based on the original calculation order
        // The preview array is created in the same order as intersections are calculated
        const index = this.intersectionPreview.findIndex(p => p.relIndex === relIdx);

        // Return 1-based index to match backend expectation (backend uses nth - 1)
        return index >= 0 ? index + 1 : undefined;
    }

    /**
     * Updates remaining preview points after construction
     */
    private updateRemainingPreviews(docCtrl: GeoDocCtrl) {
        if (this.intersectionPreview.length > this.intersectionConstructed.length) {
            this.intersectionPreview
                .filter(p => !this.intersectionConstructed.find(i => i.relIndex === p.relIndex))
                .forEach(pvP => syncPreviewCommands(pvP, docCtrl));
        }
    }
}
